import{d as D,f as p,i as V,j as L,c as w,a as t,w as l,r as a,k as n,t as f,F as N,A as P,b as s,o as g,G as A,R as E,_ as R}from"./index-C05VScWp.js";const U={class:"page-container welcome-container"},W={class:"time-display"},j={class:"card-header"},G={class:"intro-content"},H={class:"feature-item"},z={class:"feature-info"},J={class:"card-header"},O={class:"tech-stack"},Q={class:"card-header"},Y={class:"usage-guide"},q={class:"footer"},K=D({__name:"Welcome",setup(X){const b=p("");p("管理员"),p("2023-06-15 08:30:22"),p("192.168.1.100");const C=[{icon:"Menu",title:"菜单管理",desc:"灵活配置系统菜单和权限"},{icon:"UserFilled",title:"角色管理",desc:"细粒度的角色权限控制"},{icon:"Lock",title:"安全机制",desc:"JWT认证和授权"},{icon:"Setting",title:"系统配置",desc:"个性化系统参数设置"},{icon:"Histogram",title:"数据统计",desc:"多维度数据分析报表"},{icon:"Message",title:"消息通知",desc:"实时系统消息推送"}],v=()=>{b.value=new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})};let S;return V(()=>{v(),S=window.setInterval(v,1e3)}),L(()=>{clearInterval(S)}),(Z,e)=>{const k=a("Clock"),u=a("el-icon"),_=a("el-card"),d=a("el-col"),i=a("el-row"),x=a("InfoFilled"),B=a("Setting"),o=a("el-descriptions-item"),y=a("el-descriptions"),m=a("el-tab-pane"),F=a("el-tabs"),I=a("Document"),r=a("el-step"),M=a("el-steps");return g(),w("div",U,[t(i,{gutter:20},{default:l(()=>[t(d,{span:24},{default:l(()=>[t(_,{class:"welcome-header"},{default:l(()=>[e[0]||(e[0]=n("h1",null,"欢迎使用企业级管理系统",-1)),n("div",W,[t(u,null,{default:l(()=>[t(k)]),_:1}),n("span",null,f(b.value),1)])]),_:1,__:[0]})]),_:1})]),_:1}),t(i,{gutter:20,class:"mt-20"},{default:l(()=>[t(d,{span:24},{default:l(()=>[t(_,{class:"system-intro"},{header:l(()=>[n("div",j,[n("h2",null,[t(u,null,{default:l(()=>[t(x)]),_:1}),e[1]||(e[1]=s(" 系统介绍"))])])]),default:l(()=>[n("div",G,[e[2]||(e[2]=n("p",null,"本系统是一个基于微服务架构的企业级管理平台，提供完善的权限管理、用户管理、角色管理等核心功能。系统采用前后端分离设计，具有高可用性、可扩展性和安全性。",-1)),e[3]||(e[3]=n("p",null,"主要功能模块包括：",-1)),t(i,{gutter:20,class:"feature-list"},{default:l(()=>[(g(),w(N,null,P(C,(c,T)=>t(d,{span:8,key:T},{default:l(()=>[n("div",H,[t(u,null,{default:l(()=>[(g(),A(E(c.icon)))]),_:2},1024),n("div",z,[n("h3",null,f(c.title),1),n("p",null,f(c.desc),1)])])]),_:2},1024)),64))]),_:1})])]),_:1})]),_:1})]),_:1}),t(i,{gutter:20,class:"mt-20"},{default:l(()=>[t(d,{span:24},{default:l(()=>[t(_,null,{header:l(()=>[n("div",J,[n("h2",null,[t(u,null,{default:l(()=>[t(B)]),_:1}),e[4]||(e[4]=s(" 技术架构"))])])]),default:l(()=>[n("div",O,[t(F,null,{default:l(()=>[t(m,{label:"后端技术"},{default:l(()=>[t(y,{column:3,border:""},{default:l(()=>[t(o,{label:"核心框架"},{default:l(()=>e[5]||(e[5]=[s("Spring Boot 2.7.x")])),_:1,__:[5]}),t(o,{label:"微服务框架"},{default:l(()=>e[6]||(e[6]=[s("Spring Cloud 2021.0.8")])),_:1,__:[6]}),t(o,{label:"服务治理"},{default:l(()=>e[7]||(e[7]=[s("Spring Cloud Alibaba 2021.0.5.0")])),_:1,__:[7]}),t(o,{label:"ORM框架"},{default:l(()=>e[8]||(e[8]=[s("MyBatis 3.5.13 / MyBatis-Plus 4.2.0")])),_:1,__:[8]}),t(o,{label:"服务注册与配置"},{default:l(()=>e[9]||(e[9]=[s("Nacos")])),_:1,__:[9]}),t(o,{label:"服务限流与熔断"},{default:l(()=>e[10]||(e[10]=[s("Sentinel")])),_:1,__:[10]}),t(o,{label:"API网关"},{default:l(()=>e[11]||(e[11]=[s("Spring Cloud Gateway")])),_:1,__:[11]}),t(o,{label:"数据库"},{default:l(()=>e[12]||(e[12]=[s("MySQL / Redis")])),_:1,__:[12]}),t(o,{label:"安全框架"},{default:l(()=>e[13]||(e[13]=[s("Spring Security")])),_:1,__:[13]})]),_:1})]),_:1}),t(m,{label:"前端技术"},{default:l(()=>[t(y,{column:3,border:""},{default:l(()=>[t(o,{label:"核心框架"},{default:l(()=>e[14]||(e[14]=[s("Vue 3")])),_:1,__:[14]}),t(o,{label:"类型系统"},{default:l(()=>e[15]||(e[15]=[s("TypeScript")])),_:1,__:[15]}),t(o,{label:"UI组件库"},{default:l(()=>e[16]||(e[16]=[s("Element Plus")])),_:1,__:[16]}),t(o,{label:"状态管理"},{default:l(()=>e[17]||(e[17]=[s("Pinia")])),_:1,__:[17]}),t(o,{label:"路由"},{default:l(()=>e[18]||(e[18]=[s("Vue Router")])),_:1,__:[18]}),t(o,{label:"HTTP客户端"},{default:l(()=>e[19]||(e[19]=[s("Axios")])),_:1,__:[19]}),t(o,{label:"构建工具"},{default:l(()=>e[20]||(e[20]=[s("Vite")])),_:1,__:[20]}),t(o,{label:"代码规范"},{default:l(()=>e[21]||(e[21]=[s("ESLint")])),_:1,__:[21]}),t(o,{label:"CSS预处理"},{default:l(()=>e[22]||(e[22]=[s("SCSS")])),_:1,__:[22]})]),_:1})]),_:1}),t(m,{label:"系统架构"},{default:l(()=>e[23]||(e[23]=[n("div",{class:"architecture-diagram"},[n("img",{src:"https://cdn.jsdelivr.net/gh/apache/incubator-seata@develop/docs/img/architecture/Seata-Framework.png",alt:"系统架构图"})],-1)])),_:1,__:[23]})]),_:1})])]),_:1})]),_:1})]),_:1}),t(i,{gutter:20,class:"mt-20"},{default:l(()=>[t(d,{span:24},{default:l(()=>[t(_,null,{header:l(()=>[n("div",Q,[n("h2",null,[t(u,null,{default:l(()=>[t(I)]),_:1}),e[24]||(e[24]=s(" 使用指南"))])])]),default:l(()=>[n("div",Y,[t(M,{active:1,"finish-status":"success",simple:""},{default:l(()=>[t(r,{title:"登录系统",description:"使用管理员账号登录系统"}),t(r,{title:"权限配置",description:"配置用户角色和权限"}),t(r,{title:"业务管理",description:"进行日常业务操作"}),t(r,{title:"数据分析",description:"查看统计报表和分析"})]),_:1}),e[25]||(e[25]=n("div",{class:"guide-content mt-20"},[n("p",null,[s("1. "),n("strong",null,"用户管理"),s("：系统管理员可以创建用户、分配角色，管理用户权限")]),n("p",null,[s("2. "),n("strong",null,"角色管理"),s("：创建不同角色，为角色分配菜单权限")]),n("p",null,[s("3. "),n("strong",null,"菜单管理"),s("：自定义系统菜单，配置功能权限")]),n("p",null,[s("4. "),n("strong",null,"业务功能"),s("：根据权限使用系统提供的各项业务功能")]),n("p",null,[s("5. "),n("strong",null,"数据导出"),s("：支持Excel导出，方便数据处理和分析")])],-1))])]),_:1})]),_:1})]),_:1}),t(i,{class:"mt-20"},{default:l(()=>[t(d,{span:24},{default:l(()=>[n("div",q," © "+f(new Date().getFullYear())+" 企业级管理系统 | 技术支持：开发团队 ",1)]),_:1})]),_:1})])}}}),h=R(K,[["__scopeId","data-v-3d368c72"]]);export{h as default};
