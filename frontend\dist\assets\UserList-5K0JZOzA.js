import{s as A,d as J,e as T,f as c,i as K,E as r,c as D,o as w,a as l,w as o,k as W,r as i,b as g,t as X,G as N,z as E,F as Y,A as Z,N as ee,O as le,P as ae,L as te,B as oe,Q as se}from"./index-C05VScWp.js";import{g as ne}from"./role-Bbhh9P7Q.js";function re(k){return A({url:`/user-role/roleIds/${k}`,method:"get"})}function $(k,y){return A({url:`/user-role/save/${k}`,method:"post",data:y})}const ue={class:"page-container"},de={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},me=J({__name:"UserList",setup(k){const y=T({username:""}),U=c([]),x=c(1),C=c(10),z=c(0),v=c(!1),B=c(""),s=T({username:"",password:"",nickname:"",phone:"",email:"",status:1}),I=c(),_=c([]),p=c([]),P={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};function b(){ee({pageNum:x.value,pageSize:C.value,username:y.username}).then(t=>{const{records:e,total:u}=t.data;U.value=e,z.value=u}).catch(t=>{console.error("获取用户列表失败:",t),U.value=[],z.value=0})}function j(){B.value="新增用户",Object.assign(s,{id:void 0,username:"",password:"",nickname:"",phone:"",email:"",status:1}),p.value=[],v.value=!0,R()}async function q(t){if(B.value="编辑用户",Object.assign(s,t),t.id)try{const e=await re(t.id);e&&e.code===0&&e.data?le(()=>{p.value=e.data}):(p.value=[],r.error(e.msg||"获取用户角色失败"))}catch(e){console.error("获取用户角色失败:",e),p.value=[],r.error("获取用户角色失败")}else p.value=[];v.value=!0,R()}async function F(){var t;(t=I.value)==null||t.validate(async e=>{if(e)try{let u=!1,m;const d={...s};if(s.id&&d.password===""&&delete d.password,s.id){const n=await te(s.id,d);if(n&&n.code===0){r.success("编辑用户成功"),u=!0;try{await $(s.id,p.value),console.log("用户角色保存成功")}catch(V){console.error("保存用户角色失败:",V),r.warning("用户更新成功，但角色分配失败")}}else r.error((n==null?void 0:n.msg)||"编辑用户失败")}else{const n=await ae(d);if(n&&n.code===0&&n.data){if(m=n.data,r.success("新增用户成功"),u=!0,m&&p.value.length>0)try{await $(m,p.value),console.log("用户角色保存成功")}catch(V){console.error("保存用户角色失败:",V),r.warning("用户创建成功，但角色分配失败")}}else r.error((n==null?void 0:n.msg)||"新增用户失败")}u&&(v.value=!1,b())}catch(u){console.error("提交用户信息失败:",u),r.error("操作失败")}})}async function L(t){t&&oe.confirm("确定要删除该用户吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await se(t);e&&e.code===0?(r.success("删除成功"),b()):r.error((e==null?void 0:e.msg)||"删除失败")}catch(e){console.error("删除用户失败:",e),r.error("删除失败")}}).catch(()=>{r.info("已取消删除")})}K(()=>{b(),R()});async function R(){try{const t=await ne({pageNum:1,pageSize:1e3});t&&t.code===0&&t.data&&t.data.records?(_.value=[...t.data.records],console.log("All roles:",_.value)):(_.value=[],r.error(t.data.msg||"获取角色列表失败"))}catch(t){console.error("获取所有角色失败:",t),_.value=[],r.error("获取所有角色失败")}}return(t,e)=>{const u=i("el-input"),m=i("el-button"),d=i("el-table-column"),n=i("el-tag"),V=i("el-table"),M=i("el-pagination"),O=i("el-card"),f=i("el-form-item"),S=i("el-option"),h=i("el-select"),G=i("el-form"),Q=i("el-dialog");return w(),D("div",ue,[l(O,null,{default:o(()=>[W("div",de,[l(u,{modelValue:y.username,"onUpdate:modelValue":e[0]||(e[0]=a=>y.username=a),placeholder:"搜索用户名",style:{width:"200px"},clearable:""},null,8,["modelValue"]),l(m,{type:"primary",onClick:b},{default:o(()=>e[12]||(e[12]=[g("搜索")])),_:1,__:[12]}),l(m,{type:"success",onClick:j},{default:o(()=>e[13]||(e[13]=[g("新增用户")])),_:1,__:[13]})]),l(V,{data:U.value,style:{width:"100%"}},{default:o(()=>[l(d,{prop:"id",label:"ID",width:"60"}),l(d,{prop:"username",label:"用户名"}),l(d,{prop:"nickname",label:"昵称"}),l(d,{prop:"phone",label:"手机号"}),l(d,{prop:"email",label:"邮箱"}),l(d,{prop:"status",label:"状态"},{default:o(a=>[l(n,{type:a.row.status===1?"success":"info"},{default:o(()=>[g(X(a.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(d,{label:"操作",width:"180"},{default:o(a=>[l(m,{size:"small",onClick:H=>q(a.row)},{default:o(()=>e[14]||(e[14]=[g("编辑")])),_:2,__:[14]},1032,["onClick"]),l(m,{size:"small",type:"danger",onClick:H=>L(a.row.id)},{default:o(()=>e[15]||(e[15]=[g("删除")])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),l(M,{"current-page":x.value,"onUpdate:currentPage":e[1]||(e[1]=a=>x.value=a),"page-size":C.value,"onUpdate:pageSize":e[2]||(e[2]=a=>C.value=a),total:z.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:b,onCurrentChange:b,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),l(Q,{modelValue:v.value,"onUpdate:modelValue":e[11]||(e[11]=a=>v.value=a),title:B.value,width:"400px"},{footer:o(()=>[l(m,{onClick:e[10]||(e[10]=a=>v.value=!1)},{default:o(()=>e[16]||(e[16]=[g("取消")])),_:1,__:[16]}),l(m,{type:"primary",onClick:F},{default:o(()=>e[17]||(e[17]=[g("确定")])),_:1,__:[17]})]),default:o(()=>[l(G,{model:s,rules:P,ref_key:"formRef",ref:I,"label-width":"80px"},{default:o(()=>[l(f,{label:"用户名",prop:"username"},{default:o(()=>[l(u,{modelValue:s.username,"onUpdate:modelValue":e[3]||(e[3]=a=>s.username=a),autocomplete:"off"},null,8,["modelValue"])]),_:1}),s.id?E("",!0):(w(),N(f,{key:0,label:"密码",prop:"password"},{default:o(()=>[l(u,{modelValue:s.password,"onUpdate:modelValue":e[4]||(e[4]=a=>s.password=a),type:"password",autocomplete:"off"},null,8,["modelValue"])]),_:1})),l(f,{label:"昵称",prop:"nickname"},{default:o(()=>[l(u,{modelValue:s.nickname,"onUpdate:modelValue":e[5]||(e[5]=a=>s.nickname=a)},null,8,["modelValue"])]),_:1}),l(f,{label:"手机号",prop:"phone"},{default:o(()=>[l(u,{modelValue:s.phone,"onUpdate:modelValue":e[6]||(e[6]=a=>s.phone=a)},null,8,["modelValue"])]),_:1}),l(f,{label:"邮箱",prop:"email"},{default:o(()=>[l(u,{modelValue:s.email,"onUpdate:modelValue":e[7]||(e[7]=a=>s.email=a)},null,8,["modelValue"])]),_:1}),l(f,{label:"状态",prop:"status"},{default:o(()=>[l(h,{modelValue:s.status,"onUpdate:modelValue":e[8]||(e[8]=a=>s.status=a)},{default:o(()=>[l(S,{value:1,label:"启用"}),l(S,{value:0,label:"禁用"})]),_:1},8,["modelValue"])]),_:1}),_.value.length>0?(w(),N(f,{key:1,label:"分配角色",prop:"selectedRoleIds"},{default:o(()=>[l(h,{modelValue:p.value,"onUpdate:modelValue":e[9]||(e[9]=a=>p.value=a),multiple:"",placeholder:"请选择角色",style:{width:"100%"}},{default:o(()=>[(w(!0),D(Y,null,Z(_.value,a=>(w(),N(S,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):E("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{me as default};
