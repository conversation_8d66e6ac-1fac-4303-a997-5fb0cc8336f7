import{s as p,d as J,f as C,e as K,H as D,c as $,o as S,a as e,r as i,w as a,k,b as f,F as W,A as X,l as Y,E as u,G as ee,_ as le}from"./index-C05VScWp.js";function ge(n){return p.get("/lowcode/meta/entity/page",{params:n})}function te(n){return n?p.get(`/lowcode/meta/entity/${n}`):Promise.reject("ID不能为空")}function ae(n){return p.post("/lowcode/meta/entity",n)}function oe(n){return n.id?p.put("/lowcode/meta/entity",n):Promise.reject("ID不能为空")}function _e(n){return n?p.delete(`/lowcode/meta/entity/${n}`):Promise.reject("ID不能为空")}function ye(n){return n?p.post(`/lowcode/meta/entity/${n}/publish`):Promise.reject("ID不能为空")}function be(n){return n?p.post(`/lowcode/meta/entity/${n}/unpublish`):Promise.reject("ID不能为空")}const ne=[{label:"字符串",value:"String"},{label:"整数",value:"Integer"},{label:"长整数",value:"Long"},{label:"小数",value:"Double"},{label:"布尔值",value:"Boolean"},{label:"日期时间",value:"Date"},{label:"大数字",value:"BigDecimal"},{label:"文本",value:"Text"}];function re(n){return{String:"String",Integer:"Integer",Long:"Long",Double:"Double",Boolean:"Boolean",Date:"Date",BigDecimal:"BigDecimal",Text:"String"}[n]||"String"}function de(n,V){return p.post("/lowcode/meta/entity/generate-alter-sql",{entityId:n,fields:V})}function ie(n){return p.post("/lowcode/meta/entity/execute-sql",{sql:n})}const se={class:"entity-form"},ue={class:"field-list-toolbar"},me={class:"form-actions"},pe=J({__name:"EntityForm",props:{entityData:{type:Object,default:()=>null},formType:{type:String,default:"add"}},emits:["success","cancel"],setup(n,{emit:V}){const _=n,x=V,N=C(),d=K({id:void 0,entityCode:"",entityName:"",tableName:"",description:"",fields:[]}),I={entityCode:[{required:!0,message:"请输入实体编码",trigger:"blur"},{pattern:/^[a-zA-Z][a-zA-Z0-9_]*$/,message:"实体编码只能包含字母、数字和下划线，且必须以字母开头",trigger:"blur"},{min:1,max:50,message:"实体编码长度必须在1-50之间",trigger:"blur"}],entityName:[{required:!0,message:"请输入实体名称",trigger:"blur"},{min:1,max:50,message:"实体名称长度必须在1-50之间",trigger:"blur"}],tableName:[{required:!0,message:"请输入表名",trigger:"blur"}]},U={fieldCode:[{required:!0,message:"请输入字段编码",trigger:"blur"},{pattern:/^[a-zA-Z][a-zA-Z0-9_]*$/,message:"字段编码只能包含字母、数字和下划线，且必须以字母开头",trigger:"blur"},{min:1,max:50,message:"字段编码长度必须在1-50之间",trigger:"blur"}],fieldName:[{required:!0,message:"请输入字段名称",trigger:"blur"},{min:1,max:50,message:"字段名称长度必须在1-50之间",trigger:"blur"}],dataType:[{required:!0,message:"请选择数据类型",trigger:"change"}]},y=C(!1),v=C(""),T=C(!1),q=async()=>{if(_.formType==="edit"&&_.entityData)try{const o=await te(_.entityData.id);if(o.success){const t=o.data;Object.assign(d,{id:t.id,entityCode:t.entityCode,entityName:t.entityName,tableName:t.tableName,description:t.description,fields:t.fields||[]})}else u.error(o.message||"获取实体详情失败")}catch(o){console.error(o),u.error("获取实体详情失败")}else Object.assign(d,{id:void 0,entityCode:"",entityName:"",tableName:"",description:"",fields:[]})};D(()=>_.formType,q,{immediate:!0}),D(()=>_.entityData,q,{immediate:!0}),D(()=>d.entityCode,o=>{o&&!d.tableName&&(d.tableName=`lc_${o.toLowerCase()}`)});const L=()=>{d.fields.push({fieldCode:"",fieldName:"",dataType:"String",javaType:"String",length:255,required:!1,unique:!1,showInList:!0,showInForm:!0,searchable:!1})},j=o=>{d.fields.splice(o,1)},F=o=>{o.javaType=re(o.dataType),o.dataType==="String"?o.length=255:o.dataType==="Text"&&(o.length=0)},B=async()=>{if(N.value)try{if(await N.value.validate(),d.fields.forEach(o=>{o.columnName||(o.columnName=o.fieldCode.toLowerCase())}),d.id){const o=await de(d.id,d.fields);v.value=o.data||"",y.value=!0}else{const t=await ae(d);t.success?(u.success("保存成功"),x("success")):u.error(t.message||"保存失败")}}catch(o){console.error(o),u.error("表单验证失败，请检查输入")}},P=async()=>{T.value=!0;try{const o=await ie(v.value);if(o.success){u.success("SQL执行成功"),y.value=!1;const b=await oe(d);b.success?(u.success("保存成功"),x("success")):u.error(b.message||"保存失败")}else u.error(o.message||"SQL执行失败")}catch{u.error("SQL执行异常")}finally{T.value=!1}},z=()=>{x("cancel")};return(o,t)=>{const b=i("el-divider"),c=i("el-input"),m=i("el-form-item"),h=i("el-col"),E=i("el-row"),A=i("Plus"),O=i("el-icon"),g=i("el-button"),s=i("el-table-column"),Q=i("el-option"),R=i("el-select"),Z=i("el-input-number"),w=i("el-checkbox"),M=i("el-table"),G=i("el-form"),H=i("el-dialog");return S(),$("div",se,[e(G,{ref_key:"formRef",ref:N,model:d,rules:I,"label-width":"100px"},{default:a(()=>[e(b,null,{default:a(()=>t[7]||(t[7]=[f("基本信息")])),_:1,__:[7]}),e(E,{gutter:20},{default:a(()=>[e(h,{span:12},{default:a(()=>[e(m,{label:"实体编码",prop:"entityCode"},{default:a(()=>[e(c,{modelValue:d.entityCode,"onUpdate:modelValue":t[0]||(t[0]=l=>d.entityCode=l),disabled:n.formType==="edit",placeholder:"请输入实体编码，如：Customer"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(h,{span:12},{default:a(()=>[e(m,{label:"实体名称",prop:"entityName"},{default:a(()=>[e(c,{modelValue:d.entityName,"onUpdate:modelValue":t[1]||(t[1]=l=>d.entityName=l),placeholder:"请输入实体名称，如：客户"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(E,{gutter:20},{default:a(()=>[e(h,{span:12},{default:a(()=>[e(m,{label:"表名",prop:"tableName"},{default:a(()=>[e(c,{modelValue:d.tableName,"onUpdate:modelValue":t[2]||(t[2]=l=>d.tableName=l),placeholder:"请输入数据库表名，如：lc_customer"},null,8,["modelValue"])]),_:1})]),_:1}),e(h,{span:12},{default:a(()=>[e(m,{label:"描述",prop:"description"},{default:a(()=>[e(c,{modelValue:d.description,"onUpdate:modelValue":t[3]||(t[3]=l=>d.description=l),placeholder:"请输入实体描述"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b,null,{default:a(()=>t[8]||(t[8]=[f("字段信息")])),_:1,__:[8]}),k("div",ue,[e(g,{type:"primary",onClick:L},{default:a(()=>[e(O,null,{default:a(()=>[e(A)]),_:1}),t[9]||(t[9]=f(" 添加字段 "))]),_:1,__:[9]})]),e(M,{data:d.fields,border:"",style:{width:"100%"}},{default:a(()=>[e(s,{type:"index",width:"50",label:"#"}),e(s,{label:"字段编码","min-width":"120"},{default:a(l=>[e(m,{prop:"fields."+l.$index+".fieldCode",rules:U.fieldCode,class:"no-margin"},{default:a(()=>[e(c,{modelValue:l.row.fieldCode,"onUpdate:modelValue":r=>l.row.fieldCode=r,placeholder:"如：name"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"字段名称","min-width":"120"},{default:a(l=>[e(m,{prop:"fields."+l.$index+".fieldName",rules:U.fieldName,class:"no-margin"},{default:a(()=>[e(c,{modelValue:l.row.fieldName,"onUpdate:modelValue":r=>l.row.fieldName=r,placeholder:"如：姓名"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"字段描述","min-width":"120"},{default:a(l=>[e(m,{prop:"fields."+l.$index+".description",class:"no-margin"},{default:a(()=>[e(c,{modelValue:l.row.description,"onUpdate:modelValue":r=>l.row.description=r,placeholder:"如：身份证号"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(s,{label:"数据类型",width:"120"},{default:a(l=>[e(m,{prop:"fields."+l.$index+".dataType",rules:U.dataType,class:"no-margin"},{default:a(()=>[e(R,{modelValue:l.row.dataType,"onUpdate:modelValue":r=>l.row.dataType=r,placeholder:"请选择",style:{width:"100%"},onChange:r=>F(l.row)},{default:a(()=>[(S(!0),$(W,null,X(Y(ne),r=>(S(),ee(Q,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"长度",width:"120"},{default:a(l=>[e(m,{prop:"fields."+l.$index+".length",class:"no-margin"},{default:a(()=>[e(Z,{modelValue:l.row.length,"onUpdate:modelValue":r=>l.row.length=r,min:0,disabled:!["String","Text"].includes(l.row.dataType),"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop"])]),_:1}),e(s,{label:"必填",width:"70"},{default:a(l=>[e(w,{modelValue:l.row.required,"onUpdate:modelValue":r=>l.row.required=r},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(s,{label:"唯一",width:"70"},{default:a(l=>[e(w,{modelValue:l.row.unique,"onUpdate:modelValue":r=>l.row.unique=r},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(s,{label:"列表显示",width:"90"},{default:a(l=>[e(w,{modelValue:l.row.showInList,"onUpdate:modelValue":r=>l.row.showInList=r},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(s,{label:"表单显示",width:"90"},{default:a(l=>[e(w,{modelValue:l.row.showInForm,"onUpdate:modelValue":r=>l.row.showInForm=r},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(s,{label:"可搜索",width:"80"},{default:a(l=>[e(w,{modelValue:l.row.searchable,"onUpdate:modelValue":r=>l.row.searchable=r},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(s,{label:"操作",width:"80"},{default:a(l=>[e(g,{type:"danger",size:"small",link:"",onClick:r=>j(l.$index)},{default:a(()=>t[10]||(t[10]=[f(" 删除 ")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),k("div",me,[e(g,{onClick:z},{default:a(()=>t[11]||(t[11]=[f("取消")])),_:1,__:[11]}),e(g,{type:"primary",onClick:B},{default:a(()=>t[12]||(t[12]=[f("确定")])),_:1,__:[12]})])]),_:1},8,["model"]),e(H,{modelValue:y.value,"onUpdate:modelValue":t[6]||(t[6]=l=>y.value=l),title:"表结构变更SQL",width:"700px"},{footer:a(()=>[e(g,{onClick:t[5]||(t[5]=l=>y.value=!1)},{default:a(()=>t[13]||(t[13]=[f("取消")])),_:1,__:[13]}),e(g,{type:"primary",loading:T.value,onClick:P},{default:a(()=>t[14]||(t[14]=[f("执行SQL")])),_:1,__:[14]},8,["loading"])]),default:a(()=>[e(c,{type:"textarea",modelValue:v.value,"onUpdate:modelValue":t[4]||(t[4]=l=>v.value=l),rows:8,style:{"font-family":"monospace"}},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),ce=le(pe,[["__scopeId","data-v-27a8a927"]]),we=Object.freeze(Object.defineProperty({__proto__:null,default:ce},Symbol.toStringTag,{value:"Module"}));export{ce as E,we as a,_e as d,ge as g,ye as p,be as u};
