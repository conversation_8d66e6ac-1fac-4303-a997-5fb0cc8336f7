import{g as H,a as J,s as K,u as Q,d as W,b as X}from"./client-DhwCYXOu.js";import{d as Y,e as V,f as c,i as Z,c as x,o as C,a as o,w as l,k as ee,z as te,r as u,b as p,F as ne,A as oe,E as r,B as j,_ as ae}from"./index-C05VScWp.js";const le={class:"page-container"},re={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},se={key:0,style:{"margin-top":"16px"}},ie=Y({__name:"List",setup(ce){const h=V({}),_=c([]),v=c(1),w=c(10),y=c(0),z=c(!1),b=c([]),f=c(!1),k=c(""),a=V({}),O=c(),g={name:[{required:!0,message:"请输入客户名称",trigger:"blur"}],zzCode:[{required:!0,message:"请输入执照编号",trigger:"blur"}]};function B(t){const e={};return e[t]?e[t]:t.split("_").map(n=>n.charAt(0).toUpperCase()+n.slice(1)).join(" ")}function d(){H({pageNum:v.value,pageSize:w.value,...h}).then(t=>{if(t.code===0){const{records:e,total:n}=t.data;_.value=e,y.value=n}else r.error(t.msg||"获取数据失败"),_.value=[],y.value=0}).catch(t=>{console.error("获取client列表失败:",t),_.value=[],y.value=0})}function E(){Object.keys(h).forEach(t=>{h[t]=void 0}),v.value=1,d()}function F(t){b.value=t,z.value=t.length>0}function S(){Object.keys(g).forEach(t=>{a.hasOwnProperty(t)||(a[t]="")})}function D(){k.value="新增client";const t={};t.id=void 0,Object.keys(g).forEach(e=>{t.hasOwnProperty(e)||(t[e]="")}),Object.keys(a).forEach(e=>delete a[e]),Object.assign(a,t),f.value=!0}function N(t){k.value="编辑client",J(t.id).then(e=>{if(e.code===0){const n={};Object.assign(n,e.data),Object.keys(g).forEach(s=>{n.hasOwnProperty(s)||(n[s]="")}),Object.keys(a).forEach(s=>delete a[s]),Object.assign(a,n),f.value=!0}else r.error(e.msg||"获取详情失败")}).catch(e=>{console.error("获取client详情失败:",e),r.error("获取详情失败")})}async function T(){var t;(t=O.value)==null||t.validate(async e=>{if(e)try{let n;a.id?n=await Q(a.id,a):n=await K(a),n.code===0?(r.success(a.id?"编辑成功":"新增成功"),f.value=!1,d()):r.error(n.msg||"操作失败")}catch(n){console.error("提交client信息失败:",n),r.error("操作失败")}})}async function U(t){t&&j.confirm("确定要删除该client吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await W(t);e.code===0?(r.success("删除成功"),d()):r.error(e.msg||"删除失败")}catch(e){console.error("删除client失败:",e),r.error("删除失败")}}).catch(()=>{r.info("已取消删除")})}async function L(){if(!b.value.length){r.warning("请选择要删除的记录");return}j.confirm(`确定要删除选中的 ${b.value.length} 条记录吗？此操作不可恢复`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=b.value.map(n=>n.id),e=await X(t);e.code===0?(r.success("批量删除成功"),d()):r.error(e.msg||"批量删除失败")}catch(t){console.error("批量删除client失败:",t),r.error("批量删除失败")}}).catch(()=>{r.info("已取消删除")})}return Z(()=>{S(),d()}),(t,e)=>{const n=u("el-button"),s=u("el-table-column"),M=u("el-table"),P=u("el-pagination"),$=u("el-card"),A=u("el-input"),R=u("el-form-item"),q=u("el-form"),I=u("el-dialog");return C(),x("div",le,[o($,null,{default:l(()=>[ee("div",re,[o(n,{type:"primary",onClick:d},{default:l(()=>e[4]||(e[4]=[p("搜索")])),_:1,__:[4]}),o(n,{onClick:E},{default:l(()=>e[5]||(e[5]=[p("重置")])),_:1,__:[5]}),o(n,{type:"success",onClick:D},{default:l(()=>e[6]||(e[6]=[p("新增client")])),_:1,__:[6]})]),o(M,{data:_.value,style:{width:"100%"},onSelectionChange:F},{default:l(()=>[o(s,{type:"selection",width:"55"}),o(s,{prop:"name",label:"客户名称",width:"255px"}),o(s,{prop:"zzCode",label:"执照编号",width:"255px"}),o(s,{prop:"city",label:"城市",width:"255px"}),o(s,{label:"操作",width:"180"},{default:l(i=>[o(n,{size:"small",onClick:m=>N(i.row)},{default:l(()=>e[7]||(e[7]=[p("编辑")])),_:2,__:[7]},1032,["onClick"]),o(n,{size:"small",type:"danger",onClick:m=>U(i.row.id)},{default:l(()=>e[8]||(e[8]=[p("删除")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),z.value?(C(),x("div",se,[o(n,{type:"danger",onClick:L},{default:l(()=>e[9]||(e[9]=[p("批量删除")])),_:1,__:[9]})])):te("",!0),o(P,{"current-page":v.value,"onUpdate:currentPage":e[0]||(e[0]=i=>v.value=i),"page-size":w.value,"onUpdate:pageSize":e[1]||(e[1]=i=>w.value=i),total:y.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d,onCurrentChange:d,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),o(I,{modelValue:f.value,"onUpdate:modelValue":e[3]||(e[3]=i=>f.value=i),title:k.value,width:"500px"},{footer:l(()=>[o(n,{onClick:e[2]||(e[2]=i=>f.value=!1)},{default:l(()=>e[10]||(e[10]=[p("取消")])),_:1,__:[10]}),o(n,{type:"primary",onClick:T},{default:l(()=>e[11]||(e[11]=[p("确定")])),_:1,__:[11]})]),default:l(()=>[o(q,{model:a,rules:g,ref_key:"formRef",ref:O,"label-width":"100px"},{default:l(()=>[(C(),x(ne,null,oe(g,(i,m)=>o(R,{key:m,label:B(m),prop:m},{default:l(()=>[o(A,{modelValue:a[m],"onUpdate:modelValue":G=>a[m]=G},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"])),64))]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),pe=ae(ie,[["__scopeId","data-v-a4335350"]]);export{pe as default};
