import{s as C,d as W,e as q,f as b,g as B,h as E,i as F,j,c as z,k as o,a as s,w as l,r as d,E as _,u as H,o as P,l as y,m as D,n as A,p as N,q as K,b as S,t as $,v as Y,x as G,_ as J}from"./index-C05VScWp.js";function Q(h){return C({url:`/auth/captcha?id=${h}&t=${new Date().getTime()}`,method:"get",responseType:"blob"})}function X(h){return C({url:"/auth/login",method:"post",data:h})}const Z={class:"login-container"},ee={class:"login-box"},te={class:"login-left"},oe={class:"system-info"},se={class:"system-features"},ae={class:"feature-item"},re={class:"feature-item"},le={class:"feature-item"},ne={class:"login-right"},ie={class:"captcha-container"},ce=["src"],ue={class:"login-footer"},de=W({__name:"Login",setup(h){const r=q({username:"",password:"",captcha:"",remember:!1}),c=b(""),w=b(""),g=async()=>{try{w.value=Date.now().toString();const u=await Q(w.value);console.log("验证码获取：",u),c.value&&c.value.startsWith("blob:")&&URL.revokeObjectURL(c.value),c.value=URL.createObjectURL(u);const e=new Image;e.onload=()=>{console.log("图片加载成功",{naturalWidth:e.naturalWidth,naturalHeight:e.naturalHeight});const t=document.querySelector(".captcha-image img");console.log("DOM图片状态",{src:t==null?void 0:t.src,complete:t==null?void 0:t.complete,naturalWidth:t==null?void 0:t.naturalWidth,naturalHeight:t==null?void 0:t.naturalHeight})},e.onerror=t=>{console.error("图片加载失败",t);const n=new FileReader;n.onload=f=>{var a,i;console.log("Base64图片数据",(a=f.target)==null?void 0:a.result),c.value=(i=f.target)==null?void 0:i.result},n.readAsDataURL(u)},e.src=c.value}catch(u){console.error("获取验证码失败:",u),_.error("获取验证码失败，请刷新页面重试")}},U=b(),x=b(!1),M={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:4,max:6,message:"验证码长度不正确",trigger:"blur"}]},I=H(),V=B(),R=E(),L=()=>{var u;(u=U.value)==null||u.validate(async e=>{var t;if(e){x.value=!0;try{const n=await X({...r,captchaId:w.value});if(n&&n.code===0){const f=n.data;localStorage.setItem("token",f),r.remember?(localStorage.setItem("remember","true"),localStorage.setItem("username",r.username)):(localStorage.removeItem("remember"),localStorage.removeItem("username")),R.clearPermissions(),V.setMenuTree([]);try{const a=await Y();if(a&&a.code===0&&a.data){V.setMenuTree(a.data);const i=[],v=k=>{k.forEach(m=>{m.permission&&i.push(m.permission),m.children&&m.children.length>0&&v(m.children)})};v(a.data),R.setPermissions(i),G(a.data),_.success("登录成功"),setTimeout(()=>{I.push("/welcome")},100)}else _.error(a.msg||"获取菜单失败"),localStorage.removeItem("token"),g()}catch(a){console.error("获取菜单错误:",a),_.error("获取用户权限失败"),localStorage.removeItem("token"),g()}}else _.error(((t=n.data)==null?void 0:t.msg)||"登录失败"),g()}catch(n){console.error("登录请求失败:",n),_.error("登录失败，请稍后重试"),g()}finally{x.value=!1}}})};return F(()=>{if(g(),localStorage.getItem("remember")==="true"){const e=localStorage.getItem("username");e&&(r.username=e,r.remember=!0)}}),j(()=>{c.value&&c.value.startsWith("blob:")&&URL.revokeObjectURL(c.value)}),(u,e)=>{const t=d("el-icon"),n=d("DataAnalysis"),f=d("Operation"),a=d("el-input"),i=d("el-form-item"),v=d("el-checkbox"),k=d("el-link"),m=d("el-button"),O=d("el-form"),T=d("el-card");return P(),z("div",Z,[o("div",ee,[o("div",te,[o("div",oe,[e[7]||(e[7]=o("h1",{class:"system-title"},"企业级管理系统",-1)),e[8]||(e[8]=o("p",{class:"system-desc"},"基于 Spring Cloud 微服务架构的企业级管理平台",-1)),o("div",se,[o("div",ae,[s(t,null,{default:l(()=>[s(y(D))]),_:1}),e[4]||(e[4]=o("span",null,"安全可靠",-1))]),o("div",re,[s(t,null,{default:l(()=>[s(n)]),_:1}),e[5]||(e[5]=o("span",null,"数据分析",-1))]),o("div",le,[s(t,null,{default:l(()=>[s(f)]),_:1}),e[6]||(e[6]=o("span",null,"高效管理",-1))])])])]),o("div",ne,[s(T,{class:"login-card",shadow:"never"},{header:l(()=>e[9]||(e[9]=[o("div",{class:"card-header"},[o("h2",null,"系统登录"),o("p",{class:"welcome-text"},"欢迎回来，请登录您的账号")],-1)])),default:l(()=>[s(O,{model:r,rules:M,ref_key:"loginFormRef",ref:U,"label-width":"0px",class:"login-form",onKeyup:A(L,["enter"])},{default:l(()=>[s(i,{prop:"username"},{default:l(()=>[s(a,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=p=>r.username=p),placeholder:"用户名","prefix-icon":y(N),size:"large",clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),s(i,{prop:"password"},{default:l(()=>[s(a,{type:"password",modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=p=>r.password=p),placeholder:"密码","prefix-icon":y(D),"show-password":"",size:"large"},null,8,["modelValue","prefix-icon"])]),_:1}),s(i,{prop:"captcha"},{default:l(()=>[o("div",ie,[s(a,{modelValue:r.captcha,"onUpdate:modelValue":e[2]||(e[2]=p=>r.captcha=p),placeholder:"验证码","prefix-icon":y(K),size:"large"},null,8,["modelValue","prefix-icon"]),o("div",{class:"captcha-image",onClick:g},[o("img",{src:c.value,alt:"验证码"},null,8,ce)])])]),_:1}),s(i,{class:"remember-me"},{default:l(()=>[s(v,{modelValue:r.remember,"onUpdate:modelValue":e[3]||(e[3]=p=>r.remember=p)},{default:l(()=>e[10]||(e[10]=[S("记住我")])),_:1,__:[10]},8,["modelValue"]),s(k,{type:"primary",underline:!1},{default:l(()=>e[11]||(e[11]=[S("忘记密码?")])),_:1,__:[11]})]),_:1}),s(i,null,{default:l(()=>[s(m,{type:"primary",class:"login-button",onClick:L,loading:x.value,size:"large"},{default:l(()=>e[12]||(e[12]=[S(" 登录 ")])),_:1,__:[12]},8,["loading"])]),_:1})]),_:1},8,["model"]),o("div",ue,[o("p",null,"© "+$(new Date().getFullYear())+" 企业级管理系统 版权所有",1)])]),_:1})])])])}}}),pe=J(de,[["__scopeId","data-v-f2d50af4"]]);export{pe as default};
