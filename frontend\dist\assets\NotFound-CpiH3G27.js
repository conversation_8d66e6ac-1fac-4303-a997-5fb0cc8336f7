import{d as _,c as u,a as t,w as o,r as n,b as l,u as p,o as d,_ as i}from"./index-C05VScWp.js";const m={class:"forbidden-container"},f=_({__name:"NotFound",setup(x){const s=p();function r(){s.push("/welcome")}return(b,e)=>{const a=n("el-button"),c=n("el-result");return d(),u("div",m,[t(c,{icon:"error",title:"404","sub-title":"抱歉，没有找到此页面"},{extra:o(()=>[t(a,{type:"primary",onClick:r},{default:o(()=>e[0]||(e[0]=[l("返回首页")])),_:1,__:[0]})]),_:1})])}}}),N=i(f,[["__scopeId","data-v-2353c17a"]]);export{N as default};
