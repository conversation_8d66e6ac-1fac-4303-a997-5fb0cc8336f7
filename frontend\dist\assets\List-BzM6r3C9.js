import{s as _,d as K,e as B,f as u,i as Q,c as j,o as z,a as n,w as r,k as W,z as X,r as p,b as f,F as Y,A as Z,E as s,B as D,_ as ee}from"./index-C05VScWp.js";function te(a){return a.pageNum||(a.pageNum=1),a.pageSize||(a.pageSize=10),_.get("/api/test/page",{params:a})}function oe(a){return a?_.get(`/api/test/${a}`):Promise.reject("ID不能为空")}function ne(a){return _.post("/api/test",a)}function ae(a,v){return a?_.put(`/api/test/${a}`,v):Promise.reject("ID不能为空")}function le(a){return a?_.delete(`/api/test/${a}`):Promise.reject("ID不能为空")}function re(a){return!a||a.length===0?Promise.reject("ID列表不能为空"):_.delete("/api/test/batch",{data:{ids:a}})}const se={class:"page-container"},ie={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},ce={key:0,style:{"margin-top":"16px"}},ue=K({__name:"List",setup(a){const v=B({}),h=u([]),b=u(1),k=u(10),w=u(0),O=u(!1),x=u([]),g=u(!1),C=u(""),l=B({}),V=u(),y={};function E(t){const e={};return e[t]?e[t]:t.split("_").map(o=>o.charAt(0).toUpperCase()+o.slice(1)).join(" ")}function d(){te({pageNum:b.value,pageSize:k.value,...v}).then(t=>{if(t.code===0){const{records:e,total:o}=t.data;h.value=e,w.value=o}else s.error(t.msg||"获取数据失败"),h.value=[],w.value=0}).catch(t=>{console.error("获取test列表失败:",t),h.value=[],w.value=0})}function S(){Object.keys(v).forEach(t=>{v[t]=void 0}),b.value=1,d()}function F(t){x.value=t,O.value=t.length>0}function N(){Object.keys(y).forEach(t=>{l.hasOwnProperty(t)||(l[t]="")})}function P(){C.value="新增test";const t={};t.id=void 0,Object.keys(y).forEach(e=>{t.hasOwnProperty(e)||(t[e]="")}),Object.keys(l).forEach(e=>delete l[e]),Object.assign(l,t),g.value=!0}function $(t){C.value="编辑test",oe(t.id).then(e=>{if(e.code===0){const o={};Object.assign(o,e.data),Object.keys(y).forEach(i=>{o.hasOwnProperty(i)||(o[i]="")}),Object.keys(l).forEach(i=>delete l[i]),Object.assign(l,o),g.value=!0}else s.error(e.msg||"获取详情失败")}).catch(e=>{console.error("获取test详情失败:",e),s.error("获取详情失败")})}async function T(){var t;(t=V.value)==null||t.validate(async e=>{if(e)try{let o;l.id?o=await ae(l.id,l):o=await ne(l),o.code===0?(s.success(l.id?"编辑成功":"新增成功"),g.value=!1,d()):s.error(o.msg||"操作失败")}catch(o){console.error("提交test信息失败:",o),s.error("操作失败")}})}async function U(t){t&&D.confirm("确定要删除该test吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await le(t);e.code===0?(s.success("删除成功"),d()):s.error(e.msg||"删除失败")}catch(e){console.error("删除test失败:",e),s.error("删除失败")}}).catch(()=>{s.info("已取消删除")})}async function I(){if(!x.value.length){s.warning("请选择要删除的记录");return}D.confirm(`确定要删除选中的 ${x.value.length} 条记录吗？此操作不可恢复`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=x.value.map(o=>o.id),e=await re(t);e.code===0?(s.success("批量删除成功"),d()):s.error(e.msg||"批量删除失败")}catch(t){console.error("批量删除test失败:",t),s.error("批量删除失败")}}).catch(()=>{s.info("已取消删除")})}return Q(()=>{N(),d()}),(t,e)=>{const o=p("el-button"),i=p("el-table-column"),L=p("el-table"),M=p("el-pagination"),A=p("el-card"),R=p("el-input"),q=p("el-form-item"),G=p("el-form"),H=p("el-dialog");return z(),j("div",se,[n(A,null,{default:r(()=>[W("div",ie,[n(o,{type:"primary",onClick:d},{default:r(()=>e[4]||(e[4]=[f("搜索")])),_:1,__:[4]}),n(o,{onClick:S},{default:r(()=>e[5]||(e[5]=[f("重置")])),_:1,__:[5]}),n(o,{type:"success",onClick:P},{default:r(()=>e[6]||(e[6]=[f("新增test")])),_:1,__:[6]})]),n(L,{data:h.value,style:{width:"100%"},onSelectionChange:F},{default:r(()=>[n(i,{type:"selection",width:"55"}),n(i,{prop:"c1",label:"c1",width:"255px"}),n(i,{prop:"d1",label:"d1",width:"255px"}),n(i,{prop:"e",label:"e",width:"255px"}),n(i,{prop:"f",label:"f",width:"255px"}),n(i,{prop:"g1",label:"gg1",width:"255px"}),n(i,{prop:"h",label:"h",width:"255px"}),n(i,{label:"操作",width:"180"},{default:r(c=>[n(o,{size:"small",onClick:m=>$(c.row)},{default:r(()=>e[7]||(e[7]=[f("编辑")])),_:2,__:[7]},1032,["onClick"]),n(o,{size:"small",type:"danger",onClick:m=>U(c.row.id)},{default:r(()=>e[8]||(e[8]=[f("删除")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),O.value?(z(),j("div",ce,[n(o,{type:"danger",onClick:I},{default:r(()=>e[9]||(e[9]=[f("批量删除")])),_:1,__:[9]})])):X("",!0),n(M,{"current-page":b.value,"onUpdate:currentPage":e[0]||(e[0]=c=>b.value=c),"page-size":k.value,"onUpdate:pageSize":e[1]||(e[1]=c=>k.value=c),total:w.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d,onCurrentChange:d,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),n(H,{modelValue:g.value,"onUpdate:modelValue":e[3]||(e[3]=c=>g.value=c),title:C.value,width:"500px"},{footer:r(()=>[n(o,{onClick:e[2]||(e[2]=c=>g.value=!1)},{default:r(()=>e[10]||(e[10]=[f("取消")])),_:1,__:[10]}),n(o,{type:"primary",onClick:T},{default:r(()=>e[11]||(e[11]=[f("确定")])),_:1,__:[11]})]),default:r(()=>[n(G,{model:l,rules:y,ref_key:"formRef",ref:V,"label-width":"100px"},{default:r(()=>[(z(),j(Y,null,Z(y,(c,m)=>n(q,{key:m,label:E(m),prop:m},{default:r(()=>[n(R,{modelValue:l[m],"onUpdate:modelValue":J=>l[m]=J},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"])),64))]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),de=ee(ue,[["__scopeId","data-v-ffdadb3d"]]);export{de as default};
