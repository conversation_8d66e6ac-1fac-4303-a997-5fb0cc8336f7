import{u as x,s as z}from"./client-DhwCYXOu.js";import{d as V,f as w,e as p,y as k,c as C,o as E,a as o,w as r,r as s,b as f,E as d,u as q,_ as R}from"./index-C05VScWp.js";const B={class:"form-container"},F=V({__name:"Form",setup(N){const m=k(),y=q(),l=w(),t=p({}),b=p({name:[{required:!0,message:"请输入客户名称",trigger:"blur"}],zz_code:[{required:!0,message:"请输入执照编号",trigger:"blur"}]}),g=async()=>{l.value&&await l.value.validate(async i=>{if(i)try{const a=await(m.query.id?x:z)(t);a.success?(d.success("保存成功"),c()):d.error(a.message||"保存失败")}catch{d.error("操作失败")}})},c=()=>{y.back()};return m.query.id,(i,e)=>{const n=s("el-input"),a=s("el-form-item"),_=s("el-button"),v=s("el-form");return E(),C("div",B,[o(v,{ref_key:"formRef",ref:l,model:t,rules:b,"label-width":"120px",class:"form"},{default:r(()=>[o(a,{label:"客户名称",prop:"name"},{default:r(()=>[o(n,{modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=u=>t.name=u),type:"textarea",rows:3,placeholder:"请输入客户名称"},null,8,["modelValue"])]),_:1}),o(a,{label:"执照编号",prop:"zz_code"},{default:r(()=>[o(n,{modelValue:t.zz_code,"onUpdate:modelValue":e[1]||(e[1]=u=>t.zz_code=u),type:"textarea",rows:3,placeholder:"请输入执照编号"},null,8,["modelValue"])]),_:1}),o(a,null,{default:r(()=>[o(_,{type:"primary",onClick:g},{default:r(()=>e[2]||(e[2]=[f("保存")])),_:1,__:[2]}),o(_,{onClick:c},{default:r(()=>e[3]||(e[3]=[f("取消")])),_:1,__:[3]})]),_:1})]),_:1},8,["model","rules"])])}}}),D=R(F,[["__scopeId","data-v-569e79d9"]]);export{D as default};
