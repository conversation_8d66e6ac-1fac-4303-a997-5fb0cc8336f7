import{g as O,a as P,u as R,d as A}from"./member-GwsrrAP_.js";import{d as F,e as C,f as d,i as G,c as H,o as z,a as l,w as a,k as J,r as s,b as p,t as K,G as Q,z as W,E as i,B as X,_ as Y}from"./index-C05VScWp.js";const Z={class:"page-container"},h={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},ee=F({__name:"MemberList",setup(le){const g=C({username:""}),b=d([]),v=d(1),V=d(10),y=d(0),m=d(!1),w=d(""),o=C({username:"",password:"",nickname:"",phone:"",email:"",status:1}),k=d(),U={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};function f(){O({pageNum:v.value,pageSize:V.value,username:g.username}).then(n=>{const{records:e,total:r}=n.data;b.value=e,y.value=r}).catch(n=>{console.error("获取会员列表失败:",n),b.value=[],y.value=0})}function M(){w.value="新增会员",Object.assign(o,{id:void 0,username:"",password:"",nickname:"",phone:"",email:"",status:1}),m.value=!0}function B(n){w.value="编辑会员",Object.assign(o,n,{password:""}),m.value=!0}async function N(){var n;(n=k.value)==null||n.validate(async e=>{if(e)try{o.id?(await R(o.id,o),i.success("编辑成功")):(await P(o),i.success("新增成功")),m.value=!1,f()}catch(r){console.error("提交会员信息失败:",r),i.error("操作失败")}})}async function S(n){n&&X.confirm("确定要删除该会员吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await A(n);e&&e.code===0?(i.success("删除成功"),f()):i.error((e==null?void 0:e.msg)||"删除失败")}catch(e){console.error("删除会员失败:",e),i.error("删除失败")}}).catch(()=>{i.info("已取消删除")})}return G(f),(n,e)=>{const r=s("el-input"),c=s("el-button"),u=s("el-table-column"),D=s("el-tag"),E=s("el-table"),T=s("el-pagination"),j=s("el-card"),_=s("el-form-item"),x=s("el-option"),q=s("el-select"),$=s("el-form"),I=s("el-dialog");return z(),H("div",Z,[l(j,null,{default:a(()=>[J("div",h,[l(r,{modelValue:g.username,"onUpdate:modelValue":e[0]||(e[0]=t=>g.username=t),placeholder:"搜索用户名",style:{width:"200px"},clearable:""},null,8,["modelValue"]),l(c,{type:"primary",onClick:f},{default:a(()=>e[11]||(e[11]=[p("搜索")])),_:1,__:[11]}),l(c,{type:"success",onClick:M},{default:a(()=>e[12]||(e[12]=[p("新增会员")])),_:1,__:[12]})]),l(E,{data:b.value,style:{width:"100%"}},{default:a(()=>[l(u,{prop:"id",label:"ID",width:"60"}),l(u,{prop:"username",label:"用户名"}),l(u,{prop:"nickname",label:"昵称"}),l(u,{prop:"phone",label:"手机号"}),l(u,{prop:"email",label:"邮箱"}),l(u,{prop:"status",label:"状态"},{default:a(t=>[l(D,{type:t.row.status===1?"success":"info"},{default:a(()=>[p(K(t.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(u,{label:"操作",width:"180"},{default:a(t=>[l(c,{size:"small",onClick:L=>B(t.row)},{default:a(()=>e[13]||(e[13]=[p("编辑")])),_:2,__:[13]},1032,["onClick"]),l(c,{size:"small",type:"danger",onClick:L=>S(t.row.id)},{default:a(()=>e[14]||(e[14]=[p("删除")])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),l(T,{"current-page":v.value,"onUpdate:currentPage":e[1]||(e[1]=t=>v.value=t),"page-size":V.value,"onUpdate:pageSize":e[2]||(e[2]=t=>V.value=t),total:y.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:f,onCurrentChange:f,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),l(I,{modelValue:m.value,"onUpdate:modelValue":e[10]||(e[10]=t=>m.value=t),title:w.value,width:"400px"},{footer:a(()=>[l(c,{onClick:e[9]||(e[9]=t=>m.value=!1)},{default:a(()=>e[15]||(e[15]=[p("取消")])),_:1,__:[15]}),l(c,{type:"primary",onClick:N},{default:a(()=>e[16]||(e[16]=[p("确定")])),_:1,__:[16]})]),default:a(()=>[l($,{model:o,rules:U,ref_key:"formRef",ref:k,"label-width":"80px"},{default:a(()=>[l(_,{label:"用户名",prop:"username"},{default:a(()=>[l(r,{modelValue:o.username,"onUpdate:modelValue":e[3]||(e[3]=t=>o.username=t),autocomplete:"off"},null,8,["modelValue"])]),_:1}),o.id?W("",!0):(z(),Q(_,{key:0,label:"密码",prop:"password"},{default:a(()=>[l(r,{modelValue:o.password,"onUpdate:modelValue":e[4]||(e[4]=t=>o.password=t),type:"password",autocomplete:"off"},null,8,["modelValue"])]),_:1})),l(_,{label:"昵称",prop:"nickname"},{default:a(()=>[l(r,{modelValue:o.nickname,"onUpdate:modelValue":e[5]||(e[5]=t=>o.nickname=t)},null,8,["modelValue"])]),_:1}),l(_,{label:"手机号",prop:"phone"},{default:a(()=>[l(r,{modelValue:o.phone,"onUpdate:modelValue":e[6]||(e[6]=t=>o.phone=t)},null,8,["modelValue"])]),_:1}),l(_,{label:"邮箱",prop:"email"},{default:a(()=>[l(r,{modelValue:o.email,"onUpdate:modelValue":e[7]||(e[7]=t=>o.email=t)},null,8,["modelValue"])]),_:1}),l(_,{label:"状态",prop:"status"},{default:a(()=>[l(q,{modelValue:o.status,"onUpdate:modelValue":e[8]||(e[8]=t=>o.status=t)},{default:a(()=>[l(x,{value:1,label:"启用"}),l(x,{value:0,label:"禁用"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),oe=Y(ee,[["__scopeId","data-v-433753c8"]]);export{oe as default};
