import{d as J,f as p,e as g,i as K,c as U,o as b,a as o,w as l,r as d,k as w,b as c,t as f,z as j,F as H,A as O,J as Q,E as u,K as W,L as X,M as Y,G as Z,_ as ee}from"./index-C05VScWp.js";const ae={class:"my-account-container"},oe={class:"account-info"},le={key:0},se=J({__name:"MyAccount",setup(te){const i=p({username:"",status:0}),v=p([]),y=p(!0),P=p(!1),V=p(!1),C=p("basic"),k=p(),h=p(),r=g({nickname:"",phone:"",email:""}),n=g({oldPassword:"",newPassword:"",confirmPassword:""}),M=g({nickname:[{max:30,message:"昵称长度不能超过30个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]}),N=g({oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"},{validator:(a,e,s)=>{e!==n.newPassword?s(new Error("两次输入的密码不一致")):s()},trigger:"blur"}]});function q(a){if(!a)return"-";try{return new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return a}}async function R(){try{y.value=!0;const a=await Q();a&&a.code===0&&a.data?(i.value=a.data,r.nickname=a.data.nickname||"",r.phone=a.data.phone||"",r.email=a.data.email||""):u.error((a==null?void 0:a.msg)||"获取用户信息失败")}catch(a){console.error("获取用户信息失败:",a),u.error("获取用户信息失败")}finally{y.value=!1}}async function A(){try{const a=await W();a&&a.code===0&&a.data?v.value=a.data:u.error((a==null?void 0:a.msg)||"获取用户角色失败")}catch(a){console.error("获取用户角色失败:",a),u.error("获取用户角色失败")}}async function L(){k.value&&await k.value.validate(async a=>{if(a)try{P.value=!0;const e={...i.value,nickname:r.nickname,phone:r.phone,email:r.email};if(!e.id){u.error("用户ID不存在，无法更新信息");return}const s=await X(e.id,e);s&&s.code===0?(u.success("基本信息更新成功"),await R()):u.error((s==null?void 0:s.msg)||"更新基本信息失败")}catch(e){console.error("更新基本信息失败:",e),u.error("更新基本信息失败")}finally{P.value=!1}})}async function S(){if(h.value)try{if(!await h.value.validate())return;console.log("表单验证通过，准备提交密码修改请求"),V.value=!0;const e=await Y({oldPassword:n.oldPassword,newPassword:n.newPassword});console.log("密码修改请求响应:",e),e&&e.code===0?(u.success("密码修改成功，请重新登录"),n.oldPassword="",n.newPassword="",n.confirmPassword=""):u.error((e==null?void 0:e.msg)||"密码修改失败")}catch(a){console.error("密码修改失败:",a),u.error("密码修改失败: "+(a instanceof Error?a.message:String(a)))}finally{V.value=!1}}return K(async()=>{await R(),await A()}),(a,e)=>{const s=d("el-descriptions-item"),x=d("el-tag"),T=d("el-descriptions"),z=d("el-skeleton"),F=d("el-card"),I=d("el-col"),_=d("el-input"),m=d("el-form-item"),B=d("el-button"),D=d("el-form"),E=d("el-tab-pane"),$=d("el-tabs"),G=d("el-row");return b(),U("div",ae,[o(G,{gutter:20},{default:l(()=>[o(I,{span:12},{default:l(()=>[o(F,{class:"account-card"},{header:l(()=>e[7]||(e[7]=[w("div",{class:"card-header"},[w("span",null,"账号信息")],-1)])),default:l(()=>[w("div",oe,[o(z,{rows:4,animated:"",loading:y.value},{default:l(()=>[o(T,{column:1,border:""},{default:l(()=>[o(s,{label:"用户名"},{default:l(()=>[c(f(i.value.username),1)]),_:1}),o(s,{label:"昵称"},{default:l(()=>[c(f(i.value.nickname||"-"),1)]),_:1}),o(s,{label:"角色"},{default:l(()=>[(b(!0),U(H,null,O(v.value,t=>(b(),Z(x,{key:t.id,class:"role-tag"},{default:l(()=>[c(f(t.name),1)]),_:2},1024))),128)),v.value.length?j("",!0):(b(),U("span",le,"-"))]),_:1}),o(s,{label:"状态"},{default:l(()=>[o(x,{type:i.value.status===1?"success":"info"},{default:l(()=>[c(f(i.value.status===1?"正常":"禁用"),1)]),_:1},8,["type"])]),_:1}),o(s,{label:"手机号"},{default:l(()=>[c(f(i.value.phone||"-"),1)]),_:1}),o(s,{label:"邮箱"},{default:l(()=>[c(f(i.value.email||"-"),1)]),_:1}),o(s,{label:"创建时间"},{default:l(()=>[c(f(q(i.value.createTime)),1)]),_:1})]),_:1})]),_:1},8,["loading"])])]),_:1})]),_:1}),o(I,{span:12},{default:l(()=>[o(F,{class:"edit-card"},{header:l(()=>e[8]||(e[8]=[w("div",{class:"card-header"},[w("span",null,"修改信息")],-1)])),default:l(()=>[o($,{modelValue:C.value,"onUpdate:modelValue":e[6]||(e[6]=t=>C.value=t)},{default:l(()=>[o(E,{label:"基本信息",name:"basic"},{default:l(()=>[o(D,{ref_key:"basicFormRef",ref:k,model:r,rules:M,"label-width":"80px","status-icon":""},{default:l(()=>[o(m,{label:"昵称",prop:"nickname"},{default:l(()=>[o(_,{modelValue:r.nickname,"onUpdate:modelValue":e[0]||(e[0]=t=>r.nickname=t),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),o(m,{label:"手机号",prop:"phone"},{default:l(()=>[o(_,{modelValue:r.phone,"onUpdate:modelValue":e[1]||(e[1]=t=>r.phone=t),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),o(m,{label:"邮箱",prop:"email"},{default:l(()=>[o(_,{modelValue:r.email,"onUpdate:modelValue":e[2]||(e[2]=t=>r.email=t),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),o(m,null,{default:l(()=>[o(B,{type:"primary",onClick:L,loading:P.value},{default:l(()=>e[9]||(e[9]=[c("保存修改")])),_:1,__:[9]},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1}),o(E,{label:"修改密码",name:"password"},{default:l(()=>[o(D,{ref_key:"passwordFormRef",ref:h,model:n,rules:N,"label-width":"100px","status-icon":""},{default:l(()=>[o(m,{label:"原密码",prop:"oldPassword"},{default:l(()=>[o(_,{modelValue:n.oldPassword,"onUpdate:modelValue":e[3]||(e[3]=t=>n.oldPassword=t),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),o(m,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(_,{modelValue:n.newPassword,"onUpdate:modelValue":e[4]||(e[4]=t=>n.newPassword=t),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(m,{label:"确认新密码",prop:"confirmPassword"},{default:l(()=>[o(_,{modelValue:n.confirmPassword,"onUpdate:modelValue":e[5]||(e[5]=t=>n.confirmPassword=t),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(m,null,{default:l(()=>[o(B,{type:"primary",onClick:S,loading:V.value},{default:l(()=>e[10]||(e[10]=[c("修改密码")])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})])}}}),ne=ee(se,[["__scopeId","data-v-0d908678"]]);export{ne as default};
