import{g as j,a as O,u as R,d as A}from"./menu-Bg058ZKL.js";import{d as G,f as v,e as H,i as J,c as K,o as f,a as l,w as t,k as P,r as s,b as r,G as _,z as g,t as T,l as Q,I as W,E as b,B as X,_ as Y}from"./index-C05VScWp.js";const Z={class:"page-container"},h={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},ee=G({__name:"MenuList",setup(le){const k=v([]),w=v([]),y=v(!1),C=v(""),a=H({parentId:0,name:"",path:"",component:"",icon:"",type:"M",permission:"",orderNum:0,visible:1,status:1}),N=v(),E={name:[{required:!0,message:"请输入菜单名称",trigger:"blur"}],type:[{required:!0,message:"请选择菜单类型",trigger:"change"}],path:[{required:!0,message:"请输入路由路径",trigger:"blur",trigger:"change"}],permission:[{required:!0,message:"请输入权限标识",trigger:"blur"}]};async function M(){try{console.log("Fetching menu tree...");const n=await j();k.value=n.data,w.value=D(n.data)}catch(n){console.error("获取菜单列表失败:",n),k.value=[],w.value=[],b.error("获取菜单列表失败")}}function D(n){const e=[];function i(p,x=0,c=""){p.forEach(u=>{const m=c+u.name;e.push({...u,name:m}),u.children&&u.children.length>0&&i(u.children,x+1,c+"  ")})}const d=n.filter(p=>p.parentId===0||p.parentId===null);return i(d),e}function U(n){C.value="新增菜单",Object.assign(a,{id:void 0,parentId:n?n.id:0,name:"",path:"",component:"",icon:"",type:"M",permission:"",orderNum:0,visible:1,status:1}),y.value=!0}function F(n){C.value="编辑菜单",Object.assign(a,{...n,parentId:n.parentId===null?0:n.parentId}),y.value=!0}async function q(){var n;(n=N.value)==null||n.validate(async e=>{if(e)try{a.id?(await R(a.id,a),b.success("编辑成功")):(await O(a),b.success("新增成功")),y.value=!1,M()}catch(i){console.error("提交菜单信息失败:",i),b.error("操作失败")}})}async function z(n){n&&X.confirm("确定要删除该菜单吗？如果存在子菜单，将无法删除","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await A(n);e&&e.code===0?(b.success("删除成功"),M()):b.error((e==null?void 0:e.msg)||"删除失败，可能存在子菜单")}catch(e){console.error("删除菜单失败:",e),b.error("删除失败")}}).catch(()=>{b.info("已取消删除")})}return J(M),(n,e)=>{const i=s("el-button"),d=s("el-table-column"),p=s("el-tag"),x=s("el-table"),c=s("el-card"),u=s("el-form-item"),m=s("el-radio"),I=s("el-radio-group"),V=s("el-input"),S=s("el-input-number"),$=s("el-form"),L=s("el-dialog");return f(),K("div",Z,[l(c,null,{default:t(()=>[P("div",h,[l(i,{type:"success",onClick:U},{default:t(()=>e[12]||(e[12]=[r("新增菜单")])),_:1,__:[12]})]),l(x,{data:k.value,style:{width:"100%"},"row-key":"id","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:t(()=>[l(d,{prop:"name",label:"菜单名称",width:"180"}),l(d,{prop:"type",label:"类型",width:"80"},{default:t(o=>[o.row.type==="M"?(f(),_(p,{key:0},{default:t(()=>e[13]||(e[13]=[r("目录")])),_:1,__:[13]})):o.row.type==="C"?(f(),_(p,{key:1,type:"success"},{default:t(()=>e[14]||(e[14]=[r("菜单")])),_:1,__:[14]})):o.row.type==="F"?(f(),_(p,{key:2,type:"info"},{default:t(()=>e[15]||(e[15]=[r("按钮")])),_:1,__:[15]})):g("",!0)]),_:1}),l(d,{prop:"icon",label:"图标",width:"80"}),l(d,{prop:"permission",label:"权限标识"}),l(d,{prop:"path",label:"路由路径"}),l(d,{prop:"component",label:"组件路径"}),l(d,{prop:"orderNum",label:"排序",width:"80"}),l(d,{prop:"visible",label:"显示"},{default:t(o=>[r(T(o.row.visible===1?"显示":"隐藏"),1)]),_:1}),l(d,{prop:"status",label:"状态"},{default:t(o=>[l(p,{type:o.row.status===1?"success":"info"},{default:t(()=>[r(T(o.row.status===1?"正常":"停用"),1)]),_:2},1032,["type"])]),_:1}),l(d,{label:"操作",width:"200"},{default:t(o=>[o.row.type==="M"||o.row.type==="C"?(f(),_(i,{key:0,size:"small",onClick:B=>U(o.row)},{default:t(()=>e[16]||(e[16]=[r("新增")])),_:2,__:[16]},1032,["onClick"])):g("",!0),l(i,{size:"small",onClick:B=>F(o.row)},{default:t(()=>e[17]||(e[17]=[r("编辑")])),_:2,__:[17]},1032,["onClick"]),l(i,{size:"small",type:"danger",onClick:B=>z(o.row.id)},{default:t(()=>e[18]||(e[18]=[r("删除")])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),l(L,{modelValue:y.value,"onUpdate:modelValue":e[11]||(e[11]=o=>y.value=o),title:C.value,width:"500px"},{footer:t(()=>[l(i,{onClick:e[10]||(e[10]=o=>y.value=!1)},{default:t(()=>e[26]||(e[26]=[r("取消")])),_:1,__:[26]}),l(i,{type:"primary",onClick:q},{default:t(()=>e[27]||(e[27]=[r("确定")])),_:1,__:[27]})]),default:t(()=>[l($,{model:a,rules:E,ref_key:"formRef",ref:N,"label-width":"100px"},{default:t(()=>[l(u,{label:"上级菜单",prop:"parentId"},{default:t(()=>[l(Q(W),{modelValue:a.parentId,"onUpdate:modelValue":e[0]||(e[0]=o=>a.parentId=o),data:w.value,props:{value:"id",label:"name"},"node-key":"id","check-strictly":"",clearable:"",placeholder:"选择上级菜单"},null,8,["modelValue","data"])]),_:1}),l(u,{label:"菜单类型",prop:"type"},{default:t(()=>[l(I,{modelValue:a.type,"onUpdate:modelValue":e[1]||(e[1]=o=>a.type=o)},{default:t(()=>[l(m,{label:"M"},{default:t(()=>e[19]||(e[19]=[r("目录")])),_:1,__:[19]}),l(m,{label:"C"},{default:t(()=>e[20]||(e[20]=[r("菜单")])),_:1,__:[20]}),l(m,{label:"F"},{default:t(()=>e[21]||(e[21]=[r("按钮")])),_:1,__:[21]})]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"菜单名称",prop:"name"},{default:t(()=>[l(V,{modelValue:a.name,"onUpdate:modelValue":e[2]||(e[2]=o=>a.name=o),autocomplete:"off"},null,8,["modelValue"])]),_:1}),a.type!=="F"?(f(),_(u,{key:0,label:"路由路径",prop:"path"},{default:t(()=>[l(V,{modelValue:a.path,"onUpdate:modelValue":e[3]||(e[3]=o=>a.path=o)},null,8,["modelValue"])]),_:1})):g("",!0),a.type==="C"?(f(),_(u,{key:1,label:"组件路径",prop:"component"},{default:t(()=>[l(V,{modelValue:a.component,"onUpdate:modelValue":e[4]||(e[4]=o=>a.component=o)},null,8,["modelValue"])]),_:1})):g("",!0),l(u,{label:"权限标识",prop:"permission"},{default:t(()=>[l(V,{modelValue:a.permission,"onUpdate:modelValue":e[5]||(e[5]=o=>a.permission=o)},null,8,["modelValue"])]),_:1}),a.type!=="F"?(f(),_(u,{key:2,label:"图标",prop:"icon"},{default:t(()=>[l(V,{modelValue:a.icon,"onUpdate:modelValue":e[6]||(e[6]=o=>a.icon=o)},null,8,["modelValue"])]),_:1})):g("",!0),l(u,{label:"排序",prop:"orderNum"},{default:t(()=>[l(S,{modelValue:a.orderNum,"onUpdate:modelValue":e[7]||(e[7]=o=>a.orderNum=o),min:0},null,8,["modelValue"])]),_:1}),a.type!=="F"?(f(),_(u,{key:3,label:"是否显示",prop:"visible"},{default:t(()=>[l(I,{modelValue:a.visible,"onUpdate:modelValue":e[8]||(e[8]=o=>a.visible=o)},{default:t(()=>[l(m,{label:1},{default:t(()=>e[22]||(e[22]=[r("显示")])),_:1,__:[22]}),l(m,{label:0},{default:t(()=>e[23]||(e[23]=[r("隐藏")])),_:1,__:[23]})]),_:1},8,["modelValue"])]),_:1})):g("",!0),l(u,{label:"菜单状态",prop:"status"},{default:t(()=>[l(I,{modelValue:a.status,"onUpdate:modelValue":e[9]||(e[9]=o=>a.status=o)},{default:t(()=>[l(m,{label:1},{default:t(()=>e[24]||(e[24]=[r("正常")])),_:1,__:[24]}),l(m,{label:0},{default:t(()=>e[25]||(e[25]=[r("停用")])),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),ae=Y(ee,[["__scopeId","data-v-d5afcf39"]]);export{ae as default};
