import{s as o}from"./index-C05VScWp.js";async function s(){try{return await o({url:"/menu/tree",method:"get"})}catch(e){throw console.error("获取菜单树失败:",e),e}}async function c(e){try{return await o({url:"/menu",method:"post",data:e})}catch(r){throw console.error("添加菜单失败:",r),r}}async function a(e,r){try{return await o({url:`/menu/${e}`,method:"put",data:r})}catch(t){throw console.error("更新菜单失败:",t),t}}async function u(e){try{return await o({url:`/menu/${e}`,method:"delete"})}catch(r){throw console.error("删除菜单失败:",r),r}}export{c as a,u as d,s as g,a as u};
