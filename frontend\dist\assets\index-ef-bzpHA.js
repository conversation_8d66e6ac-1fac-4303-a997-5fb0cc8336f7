import{d as I,e as A,f as u,i as H,E as s,c as J,o as p,k as E,C as O,a,w as o,b as c,r,n as Q,D as R,G as _,t as V,z as S,B as h,_ as W}from"./index-C05VScWp.js";import{g as X,E as Y,d as Z,p as ee,u as te}from"./EntityForm-Cr5cSyEl.js";const ne={class:"entity-manage"},ae={class:"toolbar"},oe={class:"pagination"},le=I({__name:"index",setup(se){const l=A({page:1,size:10,keyword:""}),b=u([]),k=u(0),g=u(!1),m=u(!1),v=u(null),f=u("add");H(()=>{i()});const i=async()=>{g.value=!0;try{const n=await X(l);b.value=n.data.records,k.value=n.data.total}catch(n){console.error(n),s.error("获取实体列表失败")}finally{g.value=!1}},C=()=>{l.page=1,i()},T=n=>{l.size=n,i()},D=n=>{l.page=n,i()},x=n=>{n?(f.value="edit",v.value={...n}):(f.value="add",v.value=null),m.value=!0},N=()=>{m.value=!1,i()},P=n=>{h.confirm("确定要删除该实体吗？删除后不可恢复！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await Z(n);e.data.success?(s.success("删除成功"),i()):s.error(e.data.message||"删除失败")}catch(e){console.error(e),s.error("删除失败")}}).catch(()=>{})},$=n=>{h.confirm("确定要发布该实体吗？发布后将生成相应的数据库表和代码。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await ee(n);e.success?(s.success("发布成功"),i()):s.error(e.message||"发布失败")}catch(e){console.error(e),s.error("发布失败")}}).catch(()=>{})},F=n=>{h.confirm("确定要取消发布该实体吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await te(n);e.success?(s.success("取消发布成功"),i()):s.error(e.message||"取消发布失败")}catch(e){console.error(e),s.error("取消发布失败")}}).catch(()=>{})};return(n,e)=>{const U=r("Plus"),z=r("el-icon"),y=r("el-button"),M=r("Search"),K=r("el-input"),d=r("el-table-column"),B=r("el-tag"),L=r("el-table"),j=r("el-pagination"),q=r("el-dialog"),G=R("loading");return p(),J("div",ne,[E("div",ae,[a(y,{type:"primary",onClick:e[0]||(e[0]=t=>x())},{default:o(()=>[a(z,null,{default:o(()=>[a(U)]),_:1}),e[6]||(e[6]=c(" 新建实体 "))]),_:1,__:[6]}),a(K,{modelValue:l.keyword,"onUpdate:modelValue":e[1]||(e[1]=t=>l.keyword=t),placeholder:"请输入关键字搜索",class:"search-input",clearable:"",onKeyup:Q(C,["enter"])},{suffix:o(()=>[a(z,{class:"el-input__icon",onClick:C},{default:o(()=>[a(M)]),_:1})]),_:1},8,["modelValue"])]),O((p(),_(L,{data:b.value,border:"",style:{width:"100%"}},{default:o(()=>[a(d,{prop:"entityCode",label:"实体编码","min-width":"120"}),a(d,{prop:"entityName",label:"实体名称","min-width":"120"}),a(d,{prop:"tableName",label:"表名","min-width":"120"}),a(d,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),a(d,{label:"状态",width:"120"},{default:o(t=>[a(B,{type:t.row.published?"success":"info"},{default:o(()=>[c(V(t.row.published?"已发布":"未发布"),1)]),_:2},1032,["type"]),a(B,{type:t.row.enabled?"success":"danger",style:{"margin-left":"5px"}},{default:o(()=>[c(V(t.row.enabled?"启用":"停用"),1)]),_:2},1032,["type"])]),_:1}),a(d,{label:"操作",width:"220",fixed:"right"},{default:o(t=>[t.row.published?S("",!0):(p(),_(y,{key:0,type:"primary",size:"small",link:"",onClick:w=>x(t.row)},{default:o(()=>e[7]||(e[7]=[c(" 编辑 ")])),_:2,__:[7]},1032,["onClick"])),t.row.published?(p(),_(y,{key:2,type:"warning",size:"small",link:"",onClick:w=>F(t.row.id)},{default:o(()=>e[9]||(e[9]=[c(" 取消发布 ")])),_:2,__:[9]},1032,["onClick"])):(p(),_(y,{key:1,type:"success",size:"small",link:"",onClick:w=>$(t.row.id)},{default:o(()=>e[8]||(e[8]=[c(" 发布 ")])),_:2,__:[8]},1032,["onClick"])),t.row.published?S("",!0):(p(),_(y,{key:3,type:"danger",size:"small",link:"",onClick:w=>P(t.row.id)},{default:o(()=>e[10]||(e[10]=[c(" 删除 ")])),_:2,__:[10]},1032,["onClick"]))]),_:1})]),_:1},8,["data"])),[[G,g.value]]),E("div",oe,[a(j,{"current-page":l.page,"onUpdate:currentPage":e[2]||(e[2]=t=>l.page=t),"page-size":l.size,"onUpdate:pageSize":e[3]||(e[3]=t=>l.size=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:k.value,onSizeChange:T,onCurrentChange:D},null,8,["current-page","page-size","total"])]),a(q,{modelValue:m.value,"onUpdate:modelValue":e[5]||(e[5]=t=>m.value=t),title:f.value==="add"?"新建实体":"编辑实体",width:"60%","destroy-on-close":""},{default:o(()=>[a(Y,{"entity-data":v.value,"form-type":f.value,onSuccess:N,onCancel:e[4]||(e[4]=t=>m.value=!1)},null,8,["entity-data","form-type"])]),_:1},8,["modelValue","title"])])}}}),ce=W(le,[["__scopeId","data-v-a74090f9"]]);export{ce as default};
