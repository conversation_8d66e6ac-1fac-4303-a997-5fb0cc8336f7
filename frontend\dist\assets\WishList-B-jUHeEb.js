import{s as v,d as O,e as B,f as p,i as R,c as $,o as z,a as t,w as a,k as G,r as s,b as m,t as H,F as J,A as K,E as c,B as Q,G as X,_ as Y}from"./index-C05VScWp.js";import{b as Z}from"./member-GwsrrAP_.js";function ee(u){return v.get("/wish/page",{params:u})}function te(u){return v.post("/wish",u)}function le(u,r){return v.put(`/wish/${u}`,r)}function ae(u){return u?v.delete(`/wish/${u}`):Promise.reject("ID不能为空")}const oe={class:"page-container"},ne={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},se=O({__name:"WishList",setup(u){const r=B({keyword:"",status:""}),w=p([]),y=p(1),k=p(10),V=p(0),f=p(!1),x=p(""),n=B({content:"",status:1}),I=p(),h=p([]),D={memberId:[{required:!0,message:"请选择会员",trigger:"change"}],content:[{required:!0,message:"请输入心愿内容",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};function N(){Z().then(o=>{o&&o.code===0&&(h.value=o.data||[])}).catch(o=>{console.error("获取会员列表失败:",o),h.value=[]})}function _(){const o={pageNum:y.value,pageSize:k.value};r.keyword&&(o.keyword=r.keyword),r.status!==""&&(o.status=r.status),ee(o).then(e=>{const{records:i,total:b}=e.data;w.value=i,V.value=b}).catch(e=>{console.error("获取心愿列表失败:",e),w.value=[],V.value=0})}function S(){x.value="新增心愿",Object.assign(n,{id:void 0,memberId:void 0,content:"",status:1}),f.value=!0}function W(o){x.value="编辑心愿",Object.assign(n,o),f.value=!0}async function E(){var o;(o=I.value)==null||o.validate(async e=>{if(e)try{n.id?(await le(n.id,n),c.success("编辑成功")):(await te(n),c.success("新增成功")),f.value=!1,_()}catch(i){console.error("提交心愿信息失败:",i),c.error("操作失败")}})}async function L(o){o&&Q.confirm("确定要删除该心愿吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await ae(o);e&&e.code===0?(c.success("删除成功"),_()):c.error((e==null?void 0:e.msg)||"删除失败")}catch(e){console.error("删除心愿失败:",e),c.error("删除失败")}}).catch(()=>{c.info("已取消删除")})}return R(()=>{_(),N()}),(o,e)=>{const i=s("el-option"),b=s("el-select"),U=s("el-input"),g=s("el-button"),d=s("el-table-column"),M=s("el-tag"),T=s("el-table"),j=s("el-pagination"),q=s("el-card"),C=s("el-form-item"),A=s("el-form"),F=s("el-dialog");return z(),$("div",oe,[t(q,null,{default:a(()=>[G("div",ne,[t(b,{modelValue:r.status,"onUpdate:modelValue":e[0]||(e[0]=l=>r.status=l),placeholder:"选择状态",clearable:"",style:{width:"120px"}},{default:a(()=>[t(i,{value:1,label:"有效"}),t(i,{value:0,label:"无效"})]),_:1},8,["modelValue"]),t(U,{modelValue:r.keyword,"onUpdate:modelValue":e[1]||(e[1]=l=>r.keyword=l),placeholder:"会员用户名/昵称",style:{width:"200px"},clearable:""},null,8,["modelValue"]),t(g,{type:"primary",onClick:_},{default:a(()=>e[9]||(e[9]=[m("搜索")])),_:1,__:[9]}),t(g,{type:"success",onClick:S},{default:a(()=>e[10]||(e[10]=[m("新增心愿")])),_:1,__:[10]})]),t(T,{data:w.value,style:{width:"100%"}},{default:a(()=>[t(d,{prop:"id",label:"ID",width:"60"}),t(d,{prop:"memberId",label:"会员ID",width:"80"}),t(d,{prop:"memberUsername",label:"会员用户名",width:"120"}),t(d,{prop:"memberNickname",label:"会员昵称",width:"120"}),t(d,{prop:"content",label:"心愿内容"}),t(d,{prop:"createTime",label:"创建时间",width:"160"}),t(d,{prop:"status",label:"状态",width:"80"},{default:a(l=>[t(M,{type:l.row.status===1?"success":"info"},{default:a(()=>[m(H(l.row.status===1?"有效":"无效"),1)]),_:2},1032,["type"])]),_:1}),t(d,{label:"操作",width:"180"},{default:a(l=>[t(g,{size:"small",onClick:P=>W(l.row)},{default:a(()=>e[11]||(e[11]=[m("编辑")])),_:2,__:[11]},1032,["onClick"]),t(g,{size:"small",type:"danger",onClick:P=>L(l.row.id)},{default:a(()=>e[12]||(e[12]=[m("删除")])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),t(j,{"current-page":y.value,"onUpdate:currentPage":e[2]||(e[2]=l=>y.value=l),"page-size":k.value,"onUpdate:pageSize":e[3]||(e[3]=l=>k.value=l),total:V.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_,onCurrentChange:_,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),t(F,{modelValue:f.value,"onUpdate:modelValue":e[8]||(e[8]=l=>f.value=l),title:x.value,width:"400px"},{footer:a(()=>[t(g,{onClick:e[7]||(e[7]=l=>f.value=!1)},{default:a(()=>e[13]||(e[13]=[m("取消")])),_:1,__:[13]}),t(g,{type:"primary",onClick:E},{default:a(()=>e[14]||(e[14]=[m("确定")])),_:1,__:[14]})]),default:a(()=>[t(A,{model:n,rules:D,ref_key:"formRef",ref:I,"label-width":"80px"},{default:a(()=>[t(C,{label:"会员",prop:"memberId"},{default:a(()=>[t(b,{modelValue:n.memberId,"onUpdate:modelValue":e[4]||(e[4]=l=>n.memberId=l),filterable:"",placeholder:"请选择会员",style:{width:"100%"}},{default:a(()=>[(z(!0),$(J,null,K(h.value,l=>(z(),X(i,{key:l.id,label:`${l.id} - ${l.username}${l.nickname?" ("+l.nickname+")":""}`,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"心愿内容",prop:"content"},{default:a(()=>[t(U,{modelValue:n.content,"onUpdate:modelValue":e[5]||(e[5]=l=>n.content=l),type:"textarea",rows:4},null,8,["modelValue"])]),_:1}),t(C,{label:"状态",prop:"status"},{default:a(()=>[t(b,{modelValue:n.status,"onUpdate:modelValue":e[6]||(e[6]=l=>n.status=l)},{default:a(()=>[t(i,{value:1,label:"有效"}),t(i,{value:0,label:"无效"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),ie=Y(se,[["__scopeId","data-v-a04fb12e"]]);export{ie as default};
