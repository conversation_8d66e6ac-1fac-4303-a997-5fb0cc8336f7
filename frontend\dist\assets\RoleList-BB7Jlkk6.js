import{g as J,a as Q,u as W,d as X}from"./role-Bbhh9P7Q.js";import{s as $,d as Y,e as B,f as u,i as Z,c as ee,o as le,a as l,w as a,k as te,r as d,b as m,E as s,B as oe,_ as ae}from"./index-C05VScWp.js";import{g as ne}from"./menu-Bg058ZKL.js";function se(k){return $({url:`/role-menu/menuIds/${k}`,method:"get"})}function re(k,y){return $({url:`/role-menu/save/${k}`,method:"post",data:y})}const ue={class:"page-container"},ie={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},de=Y({__name:"RoleList",setup(k){const y=B({name:""}),V=u([]),w=u(1),x=u(10),R=u(0),c=u(!1),M=u(""),r=B({name:"",code:"",description:""}),z=u(),b=u(!1),p=u(null),C=u([]),f=u([]),v=u(null),I={name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],code:[{required:!0,message:"请输入角色编码",trigger:"blur"}]};async function g(){try{const n={pageNum:w.value,pageSize:x.value,name:y.name},e=await J(n),{records:t,total:i}=e.data;V.value=t,R.value=i}catch(n){console.error("获取角色列表失败:",n),V.value=[],R.value=0,s.error("获取角色列表失败")}}function S(){M.value="新增角色",Object.assign(r,{id:void 0,name:"",code:"",description:""}),c.value=!0}function A(n){M.value="编辑角色",Object.assign(r,n),c.value=!0}async function E(){var n;(n=z.value)==null||n.validate(async e=>{if(e)try{r.id?(await W(r.id,r),s.success("编辑成功")):(await Q(r),s.success("新增成功")),c.value=!1,g()}catch(t){console.error("提交角色信息失败:",t),s.error("操作失败")}})}async function K(n){n&&oe.confirm("确定要删除该角色吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await X(n);e&&e.code===0?(s.success("删除成功"),g()):s.error((e==null?void 0:e.msg)||"删除失败")}catch(e){console.error("删除角色失败:",e),s.error("删除失败")}}).catch(()=>{s.info("已取消删除")})}function N(){p.value&&O(p.value)}function h(){v.value&&v.value.setCheckedKeys([]),f.value=[]}async function O(n){try{const e=await ne();if(console.log("menuTreeRes",e),e&&e.code===0&&e.data?C.value=e.data:(s.error(e.msg||"获取菜单树失败"),C.value=[]),n.id){const t=await se(n.id);console.log("assignedMenuRes",t),t&&t.code===0&&t.data?setTimeout(()=>{f.value=t.data,v.value&&v.value.setCheckedKeys(t.data)},100):(s.error(t.msg||"获取已分配菜单失败"),f.value=[])}else f.value=[]}catch(e){console.error("获取菜单数据失败:",e),s.error("获取菜单数据失败"),C.value=[],f.value=[]}}async function j(n){p.value=n,b.value=!0}async function q(){if(!p.value||!p.value.id){s.warning("请选择要分配菜单的角色");return}const e=v.value.getCheckedKeys();try{const t=await re(p.value.id,e);t&&t.code===0?(s.success("分配菜单成功"),b.value=!1):s.error(t.msg||"分配菜单失败")}catch(t){console.error("保存分配菜单失败:",t),s.error("保存分配菜单失败")}}return Z(g),(n,e)=>{const t=d("el-input"),i=d("el-button"),_=d("el-table-column"),L=d("el-table"),P=d("el-pagination"),F=d("el-card"),T=d("el-form-item"),G=d("el-form"),D=d("el-dialog"),H=d("el-tree");return le(),ee("div",ue,[l(F,null,{default:a(()=>[te("div",ie,[l(t,{modelValue:y.name,"onUpdate:modelValue":e[0]||(e[0]=o=>y.name=o),placeholder:"搜索角色名称",style:{width:"200px"},clearable:""},null,8,["modelValue"]),l(i,{type:"primary",onClick:g},{default:a(()=>e[10]||(e[10]=[m("搜索")])),_:1,__:[10]}),l(i,{type:"success",onClick:S},{default:a(()=>e[11]||(e[11]=[m("新增角色")])),_:1,__:[11]})]),l(L,{data:V.value,style:{width:"100%"}},{default:a(()=>[l(_,{prop:"id",label:"ID",width:"60"}),l(_,{prop:"name",label:"角色名称"}),l(_,{prop:"description",label:"角色描述"}),l(_,{prop:"createTime",label:"创建时间"}),l(_,{prop:"updateTime",label:"更新时间"}),l(_,{label:"操作",width:"280"},{default:a(o=>[l(i,{size:"small",onClick:U=>A(o.row)},{default:a(()=>e[12]||(e[12]=[m("编辑")])),_:2,__:[12]},1032,["onClick"]),l(i,{size:"small",type:"danger",onClick:U=>K(o.row.id)},{default:a(()=>e[13]||(e[13]=[m("删除")])),_:2,__:[13]},1032,["onClick"]),l(i,{size:"small",onClick:U=>j(o.row)},{default:a(()=>e[14]||(e[14]=[m("分配权限")])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),l(P,{"current-page":w.value,"onUpdate:currentPage":e[1]||(e[1]=o=>w.value=o),"page-size":x.value,"onUpdate:pageSize":e[2]||(e[2]=o=>x.value=o),total:R.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:g,onCurrentChange:g,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),l(D,{modelValue:c.value,"onUpdate:modelValue":e[7]||(e[7]=o=>c.value=o),title:M.value,width:"400px"},{footer:a(()=>[l(i,{onClick:e[6]||(e[6]=o=>c.value=!1)},{default:a(()=>e[15]||(e[15]=[m("取消")])),_:1,__:[15]}),l(i,{type:"primary",onClick:E},{default:a(()=>e[16]||(e[16]=[m("确定")])),_:1,__:[16]})]),default:a(()=>[l(G,{model:r,rules:I,ref_key:"formRef",ref:z,"label-width":"80px"},{default:a(()=>[l(T,{label:"角色名称",prop:"name"},{default:a(()=>[l(t,{modelValue:r.name,"onUpdate:modelValue":e[3]||(e[3]=o=>r.name=o),autocomplete:"off"},null,8,["modelValue"])]),_:1}),l(T,{label:"角色编码",prop:"code"},{default:a(()=>[l(t,{modelValue:r.code,"onUpdate:modelValue":e[4]||(e[4]=o=>r.code=o),autocomplete:"off"},null,8,["modelValue"])]),_:1}),l(T,{label:"角色描述",prop:"description"},{default:a(()=>[l(t,{modelValue:r.description,"onUpdate:modelValue":e[5]||(e[5]=o=>r.description=o),type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(D,{modelValue:b.value,"onUpdate:modelValue":e[9]||(e[9]=o=>b.value=o),title:"分配权限",width:"400px",onOpen:N,onClose:h},{footer:a(()=>[l(i,{onClick:e[8]||(e[8]=o=>b.value=!1)},{default:a(()=>e[17]||(e[17]=[m("取消")])),_:1,__:[17]}),l(i,{type:"primary",onClick:q},{default:a(()=>e[18]||(e[18]=[m("确定")])),_:1,__:[18]})]),default:a(()=>[l(H,{ref_key:"menuTreeRef",ref:v,data:C.value,"show-checkbox":"","node-key":"id","default-checked-keys":f.value,props:{children:"children",label:"name"}},null,8,["data","default-checked-keys"])]),_:1},8,["modelValue"])])}}}),fe=ae(de,[["__scopeId","data-v-debb3f1b"]]);export{fe as default};
