import{d as _,c as i,a as t,w as o,r as n,b as l,u as d,o as u,_ as p}from"./index-C05VScWp.js";const m={class:"forbidden-container"},f=_({__name:"Forbidden",setup(b){const s=d();function r(){s.push("/welcome")}return(x,e)=>{const a=n("el-button"),c=n("el-result");return u(),i("div",m,[t(c,{icon:"warning",title:"403","sub-title":"抱歉，您没有权限访问此页面"},{extra:o(()=>[t(a,{type:"primary",onClick:r},{default:o(()=>e[0]||(e[0]=[l("返回首页")])),_:1,__:[0]})]),_:1})])}}}),C=p(f,[["__scopeId","data-v-abc1e38f"]]);export{C as default};
