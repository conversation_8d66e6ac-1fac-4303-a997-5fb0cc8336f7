import{s as _,d as K,e as B,f as c,i as Q,c as j,o as z,a as o,w as l,k as W,z as X,r as f,b as d,F as Y,A as Z,E as s,B as D,_ as ee}from"./index-C05VScWp.js";function te(n){return n.pageNum||(n.pageNum=1),n.pageSize||(n.pageSize=10),_.get("/api/staff/page",{params:n})}function ae(n){return n?_.get(`/api/staff/${n}`):Promise.reject("ID不能为空")}function ne(n){return _.post("/api/staff",n)}function oe(n,v){return n?_.put(`/api/staff/${n}`,v):Promise.reject("ID不能为空")}function re(n){return n?_.delete(`/api/staff/${n}`):Promise.reject("ID不能为空")}function le(n){return!n||n.length===0?Promise.reject("ID列表不能为空"):_.delete("/api/staff/batch",{data:{ids:n}})}const se={class:"page-container"},ie={style:{"margin-bottom":"16px",display:"flex",gap:"8px","align-items":"center"}},ue={key:0,style:{"margin-top":"16px"}},ce=K({__name:"List",setup(n){const v=B({}),b=c([]),h=c(1),x=c(10),w=c(0),O=c(!1),k=c([]),g=c(!1),C=c(""),r=B({}),V=c(),y={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"}]};function E(t){const e={};return e[t]?e[t]:t.split("_").map(a=>a.charAt(0).toUpperCase()+a.slice(1)).join(" ")}function p(){te({pageNum:h.value,pageSize:x.value,...v}).then(t=>{if(t.code===0){const{records:e,total:a}=t.data;b.value=e,w.value=a}else s.error(t.msg||"获取数据失败"),b.value=[],w.value=0}).catch(t=>{console.error("获取staff列表失败:",t),b.value=[],w.value=0})}function S(){Object.keys(v).forEach(t=>{v[t]=void 0}),h.value=1,p()}function F(t){k.value=t,O.value=t.length>0}function N(){Object.keys(y).forEach(t=>{r.hasOwnProperty(t)||(r[t]="")})}function P(){C.value="新增staff";const t={};t.id=void 0,Object.keys(y).forEach(e=>{t.hasOwnProperty(e)||(t[e]="")}),Object.keys(r).forEach(e=>delete r[e]),Object.assign(r,t),g.value=!0}function $(t){C.value="编辑staff",ae(t.id).then(e=>{if(e.code===0){const a={};Object.assign(a,e.data),Object.keys(y).forEach(u=>{a.hasOwnProperty(u)||(a[u]="")}),Object.keys(r).forEach(u=>delete r[u]),Object.assign(r,a),g.value=!0}else s.error(e.msg||"获取详情失败")}).catch(e=>{console.error("获取staff详情失败:",e),s.error("获取详情失败")})}async function T(){var t;(t=V.value)==null||t.validate(async e=>{if(e)try{let a;r.id?a=await oe(r.id,r):a=await ne(r),a.code===0?(s.success(r.id?"编辑成功":"新增成功"),g.value=!1,p()):s.error(a.msg||"操作失败")}catch(a){console.error("提交staff信息失败:",a),s.error("操作失败")}})}async function U(t){t&&D.confirm("确定要删除该staff吗？此操作不可恢复","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await re(t);e.code===0?(s.success("删除成功"),p()):s.error(e.msg||"删除失败")}catch(e){console.error("删除staff失败:",e),s.error("删除失败")}}).catch(()=>{s.info("已取消删除")})}async function I(){if(!k.value.length){s.warning("请选择要删除的记录");return}D.confirm(`确定要删除选中的 ${k.value.length} 条记录吗？此操作不可恢复`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const t=k.value.map(a=>a.id),e=await le(t);e.code===0?(s.success("批量删除成功"),p()):s.error(e.msg||"批量删除失败")}catch(t){console.error("批量删除staff失败:",t),s.error("批量删除失败")}}).catch(()=>{s.info("已取消删除")})}return Q(()=>{N(),p()}),(t,e)=>{const a=f("el-button"),u=f("el-table-column"),L=f("el-table"),M=f("el-pagination"),A=f("el-card"),R=f("el-input"),q=f("el-form-item"),G=f("el-form"),H=f("el-dialog");return z(),j("div",se,[o(A,null,{default:l(()=>[W("div",ie,[o(a,{type:"primary",onClick:p},{default:l(()=>e[4]||(e[4]=[d("搜索")])),_:1,__:[4]}),o(a,{onClick:S},{default:l(()=>e[5]||(e[5]=[d("重置")])),_:1,__:[5]}),o(a,{type:"success",onClick:P},{default:l(()=>e[6]||(e[6]=[d("新增staff")])),_:1,__:[6]})]),o(L,{data:b.value,style:{width:"100%"},onSelectionChange:F},{default:l(()=>[o(u,{type:"selection",width:"55"}),o(u,{prop:"name",label:"姓名",width:"50px"}),o(u,{prop:"email",label:"邮箱",width:"255px"}),o(u,{label:"操作",width:"180"},{default:l(i=>[o(a,{size:"small",onClick:m=>$(i.row)},{default:l(()=>e[7]||(e[7]=[d("编辑")])),_:2,__:[7]},1032,["onClick"]),o(a,{size:"small",type:"danger",onClick:m=>U(i.row.id)},{default:l(()=>e[8]||(e[8]=[d("删除")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),O.value?(z(),j("div",ue,[o(a,{type:"danger",onClick:I},{default:l(()=>e[9]||(e[9]=[d("批量删除")])),_:1,__:[9]})])):X("",!0),o(M,{"current-page":h.value,"onUpdate:currentPage":e[0]||(e[0]=i=>h.value=i),"page-size":x.value,"onUpdate:pageSize":e[1]||(e[1]=i=>x.value=i),total:w.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:p,onCurrentChange:p,style:{"margin-top":"16px","text-align":"right"}},null,8,["current-page","page-size","total"])]),_:1}),o(H,{modelValue:g.value,"onUpdate:modelValue":e[3]||(e[3]=i=>g.value=i),title:C.value,width:"500px"},{footer:l(()=>[o(a,{onClick:e[2]||(e[2]=i=>g.value=!1)},{default:l(()=>e[10]||(e[10]=[d("取消")])),_:1,__:[10]}),o(a,{type:"primary",onClick:T},{default:l(()=>e[11]||(e[11]=[d("确定")])),_:1,__:[11]})]),default:l(()=>[o(G,{model:r,rules:y,ref_key:"formRef",ref:V,"label-width":"100px"},{default:l(()=>[(z(),j(Y,null,Z(y,(i,m)=>o(q,{key:m,label:E(m),prop:m},{default:l(()=>[o(R,{modelValue:r[m],"onUpdate:modelValue":J=>r[m]=J},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"])),64))]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),pe=ee(ce,[["__scopeId","data-v-043256f2"]]);export{pe as default};
